<?php

	date_default_timezone_set('Asia/Calcutta');
	$messege ="";

	class main
	{
		public $downloads, $no_rows_downloads, $news_letter, $no_rows_news_letter, $no_rows, $data, $id;
		
		function connection()
		{
			mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);
			set_exception_handler(function($e) {
			error_log($e->getMessage());
			// Return JSON error for AJAX requests
			if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
				header('Content-Type: application/json');
				echo json_encode(['msg' => 'Database Error: ' . $e->getMessage(), 'response' => 'E']);
			} else {
				exit('Error connecting to database'); //Should be a message a typical user could understand
			}
			exit();
			});
			$mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);
			//$mysqli->set_charset("utf8mb4");
			return $mysqli;
		}

		
		function fSelectRowCountNew($Query)
			{
				$mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);
				$stmt = $mysqli->query($Query);
				$totalRows = $stmt->num_rows;
				$stmt->close();
				return $totalRows;
			}


		function fSelectStringNew($Query,$Column)
		{
			$totalRows=0;
			$mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);
            $stmt = $mysqli->query($Query);
            if ($stmt->num_rows > 0) {
                while ($row = mysqli_fetch_assoc($stmt))
				{ 
					$totalRows = $row[$Column];
				}
            }
            $stmt->close();
			return $totalRows;
		}
		
		function MysqliSelect($Query,$FieldNames)
		{
			
			$resultarray=array(array());
			$resultarray1=array(array());
			
			$mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);
			$stmt = $mysqli->stmt_init();
			$stmt = $mysqli->prepare($Query);
			$stmt->execute();
			$stmt->store_result();
		
			switch(count($FieldNames))
			{
						case 0 :
						break;
						case 1 : $stmt->bind_result($resultarray[0]); 
						break;
						case 2 : $stmt->bind_result($resultarray[0],$resultarray[1]); 
						break;
						case 3 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2]); 
						break;
						case 4 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3]); 
						break;
						case 5 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4]); 
						break;
						case 6 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5]); 
						break;
						case 7 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6]); 
						break;
						case 8 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7]); 
						 break;
						 case 9 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8]); 
						 break;
						 case 10 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9]); 
						 break;
						 case 11 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[10]);
						 case 11 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10]); break;
                        case 12 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11]); break;
                        case 13 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12]); break;
                        case 14 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13]); break;
                        case 15 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14]); break;
                        case 16 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15]); break;
                        case 17 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16]); break;
                        case 18 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17]); break;
                        case 19 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18]); break;
                        case 20 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19]); break;
                        case 21 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20]); break;
                        case 22 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21]); break;
                        case 23 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22]); break;
                        case 24 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23]); break;
                        case 25 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24]); break;
                        case 26 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25]); break;
                        case 27 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26]); break;
                        case 28 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27]); break;
                        case 29 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28]); break;
                        case 30 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29]); break;
                        case 31 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30]); break;
                        case 32 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31]); break;
                        case 33 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32]); break;
                        case 34 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33]); break;
                        case 35 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34]); break;
                        case 36 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35]); break;
                        case 37 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36]); break;
                        case 38 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37]); break;
                        case 39 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38]); break;
                        case 40 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39]); break;
                        case 41 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40]); break;
                        case 42 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41]); break;
                        case 43 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42]); break;
                        case 44 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43]); break;
                        case 45 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44]); break;
                        case 46 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45]); break;
                        case 47 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46]); break;
                        case 48 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47]); break;
                        case 49 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48]); break;
                        case 50 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49]); break;
                        case 51 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50]); break;
                        case 52 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51]); break;
                        case 53 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52]); break;
                        case 54 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53]); break;
                        case 55 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54]); break;
                        case 56 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55]); break;
                        case 57 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56]); break;
                        case 58 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57]); break;
                        case 59 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58]); break;
                        case 60 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59]); break;
                        case 61 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60]); break;
                        case 62 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61]); break;
                        case 63 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62]); break;
                        case 64 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63]); break;
                        case 65 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64]); break;
                        case 66 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65]); break;
                        case 67 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66]); break;
                        case 68 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67]); break;
                        case 69 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68]); break;
                        case 70 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69]); break;
                        case 71 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70]); break;
                        case 72 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71]); break;
                        case 73 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72]); break;
                        case 74 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73]); break;
                        case 75 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74]); break;
                        case 76 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75]); break;
                        case 77 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76]); break;
                        case 78 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77]); break;
                        case 79 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78]); break;
                        case 80 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79]); break;
                        case 81 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80]); break;
                        case 82 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81]); break;
                        case 83 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82]); break;
                        case 84 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83]); break;
                        case 85 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84]); break;
                        case 86 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85]); break;
                        case 87 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86]); break;
                        case 88 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87]); break;
                        case 89 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88]); break;
                        case 90 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89]); break;
                        case 91 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90]); break;
                        case 92 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91]); break;
                        case 93 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92]); break;
                        case 94 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93]); break;
                        case 95 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94]); break;
                        case 96 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95]); break;
                        case 97 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95],$resultarray[96]); break;
                        case 98 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95],$resultarray[96],$resultarray[97]); break;
                        case 99 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95],$resultarray[96],$resultarray[97],$resultarray[98]); break;

			}
			$Count=0;
			$ColumnCounter=0;
			while($stmt->fetch()) {
				for($i=0; $i<count($FieldNames); $i++)
				{
					$resultarray1[$Count][$FieldNames[$i]] = $resultarray[$i];
				}
				$Count++;
				
			}
			  //var_export($result1);
			$stmt->close();
			return $resultarray1;
		}

		function MysqliSelect1($Query,$FieldNames,$sequence,$ParamArray)
		{
			
			$resultarray=array(array());
			$resultarray1=array(array());
			
			$mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);
			$stmt = $mysqli->stmt_init();
			$stmt = $mysqli->prepare($Query);
			switch(count($ParamArray))
			{
			            case 0 : 
						break;
						case 1 : $stmt->bind_param($sequence, $ParamArray[0]);
						break;
						case 2 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1]);
						break;
						 case 3 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2]);
						 break;
						 case 4 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3]);
						 break;
						 case 5 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4]);
						 break;
						 case 6 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5]);
						 break;
						 case 7 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6]);
						 break;
						 case 8 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7]);
						 break;
						 case 9 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8]);
						 break;
						 case 10 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9]);
						 break;
						 case 11 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10]);
						 break;
						 case 12 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11]);
						 break;
						 case 13 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12]);
						 break;
						 case 14 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13]);
						 break;
						 case 15 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14]);
						 break;
						 case 16 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15]);
						 break;
						 case 17 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16]);
						 break;
						 case 18 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17]);
						 break;
						 case 19 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18]);
						 break;
						 case 20 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19]);
						 break;
						 case 21 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20]);
						 break;
						 case 22 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21]);
						 break;
						 case 23 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22]);
						 break;
						 case 24 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23]);
						 break;
						 case 25 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24]);
						 break;
						 case 26 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25]);
						 break;
						 case 27 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26]); break;
                        case 28 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27]); break;
                        case 29 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28]); break;
                        case 30 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29]); break;
                        case 31 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30]); break;
                        case 32 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31]); break;
                        case 33 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32]); break;
                        case 34 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33]); break;
                        case 35 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34]); break;
                        case 36 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35]); break;
                        case 37 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36]); break;
                        case 38 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37]); break;
                        case 39 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38]); break;
                        case 40 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39]); break;
                        case 41 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40]); break;
                        case 42 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41]); break;
                        case 43 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42]); break;
                        case 44 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43]); break;
                        case 45 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44]); break;
                        case 46 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45]); break;
                        case 47 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46]); break;
                        case 48 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47]); break;
                        case 49 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48]); break;
                        case 50 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49]); break;
                        case 51 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50]); break;
                        case 52 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51]); break;
                        case 53 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52]); break;
                        case 54 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53]); break;
                        case 55 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54]); break;
                        case 56 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55]); break;
                        case 57 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56]); break;
                        case 58 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57]); break;
                        case 59 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58]); break;
                        case 60 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59]); break;
                        case 61 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60]); break;
                        case 62 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61]); break;
                        case 63 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62]); break;
                        case 64 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63]); break;
                        case 65 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64]); break;
                        case 66 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65]); break;
                        case 67 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66]); break;
                        case 68 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67]); break;
                        case 69 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68]); break;
                        case 70 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69]); break;
                        case 71 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70]); break;
                        case 72 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71]); break;
                        case 73 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72]); break;
                        case 74 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73]); break;
                        case 75 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74]); break;
                        case 76 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75]); break;
                        case 77 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76]); break;
                        case 78 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77]); break;
                        case 79 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78]); break;
                        case 80 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79]); break;
                        case 81 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80]); break;
                        case 82 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81]); break;
                        case 83 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82]); break;
                        case 84 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83]); break;
                        case 85 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84]); break;
                        case 86 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85]); break;
                        case 87 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86]); break;
                        case 88 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87]); break;
                        case 89 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88]); break;
                        case 90 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89]); break;
                        case 91 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89],$ParamArray[90]); break;
                        case 92 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89],$ParamArray[90],$ParamArray[91]); break;
                        case 93 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89],$ParamArray[90],$ParamArray[91],$ParamArray[92]); break;
                        case 94 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89],$ParamArray[90],$ParamArray[91],$ParamArray[92],$ParamArray[93]); break;
                        case 95 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89],$ParamArray[90],$ParamArray[91],$ParamArray[92],$ParamArray[93],$ParamArray[94]); break;
                        case 96 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89],$ParamArray[90],$ParamArray[91],$ParamArray[92],$ParamArray[93],$ParamArray[94],$ParamArray[95]); break;
                        case 97 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89],$ParamArray[90],$ParamArray[91],$ParamArray[92],$ParamArray[93],$ParamArray[94],$ParamArray[95],$ParamArray[96]); break;
                        case 98 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89],$ParamArray[90],$ParamArray[91],$ParamArray[92],$ParamArray[93],$ParamArray[94],$ParamArray[95],$ParamArray[96],$ParamArray[97]); break;
                        case 99 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89],$ParamArray[90],$ParamArray[91],$ParamArray[92],$ParamArray[93],$ParamArray[94],$ParamArray[95],$ParamArray[96],$ParamArray[97],$ParamArray[98]); break;
                        case 100 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30],$ParamArray[31],$ParamArray[32],$ParamArray[33],$ParamArray[34],$ParamArray[35],$ParamArray[36],$ParamArray[37],$ParamArray[38],$ParamArray[39],$ParamArray[40],$ParamArray[41],$ParamArray[42],$ParamArray[43],$ParamArray[44],$ParamArray[45],$ParamArray[46],$ParamArray[47],$ParamArray[48],$ParamArray[49],$ParamArray[50],$ParamArray[51],$ParamArray[52],$ParamArray[53],$ParamArray[54],$ParamArray[55],$ParamArray[56],$ParamArray[57],$ParamArray[58],$ParamArray[59],$ParamArray[60],$ParamArray[61],$ParamArray[62],$ParamArray[63],$ParamArray[64],$ParamArray[65],$ParamArray[66],$ParamArray[67],$ParamArray[68],$ParamArray[69],$ParamArray[70],$ParamArray[71],$ParamArray[72],$ParamArray[73],$ParamArray[74],$ParamArray[75],$ParamArray[76],$ParamArray[77],$ParamArray[78],$ParamArray[79],$ParamArray[80],$ParamArray[81],$ParamArray[82],$ParamArray[83],$ParamArray[84],$ParamArray[85],$ParamArray[86],$ParamArray[87],$ParamArray[88],$ParamArray[89],$ParamArray[90],$ParamArray[91],$ParamArray[92],$ParamArray[93],$ParamArray[94],$ParamArray[95],$ParamArray[96],$ParamArray[97],$ParamArray[98],$ParamArray[99]); break;
						 
			}
			
			//$stmt = $mysqli->bind_param($Query);
			$stmt->execute();
			$stmt->store_result();
		
			switch(count($FieldNames))
			{
			             case 0 : 
						break;
						case 1 : $stmt->bind_result($resultarray[0]); 
						break;
						case 2 : $stmt->bind_result($resultarray[0],$resultarray[1]); 
						break;
						 case 3 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2]); 
						 break;
						 case 4 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3]); 
						 break;
						 case 5 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4]); 
						 break;
						 case 6 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5]); 
						 break;
						 case 7 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6]); 
						 break;
						 case 8 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7]); 
						 break;
						 case 9 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8]); 
						 break;
						 case 10 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9]); 
						 break;
						 
						case 11 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10]); break;
                        case 12 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11]); break;
                        case 13 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12]); break;
                        case 14 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13]); break;
                        case 15 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14]); break;
                        case 16 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15]); break;
                        case 17 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16]); break;
                        case 18 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17]); break;
                        case 19 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18]); break;
                        case 20 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19]); break;
                        case 21 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20]); break;
                        case 22 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21]); break;
                        case 23 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22]); break;
                        case 24 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23]); break;
                        case 25 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24]); break;
                        case 26 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25]); break;
                        case 27 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26]); break;
                        case 28 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27]); break;
                        case 29 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28]); break;
                        case 30 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29]); break;
                        case 31 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30]); break;
                        case 32 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31]); break;
                        case 33 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32]); break;
                        case 34 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33]); break;
                        case 35 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34]); break;
                        case 36 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35]); break;
                        case 37 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36]); break;
                        case 38 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37]); break;
                        case 39 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38]); break;
                        case 40 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39]); break;
                        case 41 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40]); break;
                        case 42 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41]); break;
                        case 43 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42]); break;
                        case 44 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43]); break;
                        case 45 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44]); break;
                        case 46 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45]); break;
                        case 47 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46]); break;
                        case 48 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47]); break;
                        case 49 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48]); break;
                        case 50 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49]); break;
                        case 51 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50]); break;
                        case 52 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51]); break;
                        case 53 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52]); break;
                        case 54 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53]); break;
                        case 55 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54]); break;
                        case 56 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55]); break;
                        case 57 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56]); break;
                        case 58 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57]); break;
                        case 59 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58]); break;
                        case 60 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59]); break;
                        case 61 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60]); break;
                        case 62 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61]); break;
                        case 63 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62]); break;
                        case 64 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63]); break;
                        case 65 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64]); break;
                        case 66 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65]); break;
                        case 67 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66]); break;
                        case 68 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67]); break;
                        case 69 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68]); break;
                        case 70 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69]); break;
                        case 71 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70]); break;
                        case 72 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71]); break;
                        case 73 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72]); break;
                        case 74 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73]); break;
                        case 75 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74]); break;
                        case 76 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75]); break;
                        case 77 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76]); break;
                        case 78 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77]); break;
                        case 79 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78]); break;
                        case 80 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79]); break;
                        case 81 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80]); break;
                        case 82 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81]); break;
                        case 83 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82]); break;
                        case 84 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83]); break;
                        case 85 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84]); break;
                        case 86 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85]); break;
                        case 87 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86]); break;
                        case 88 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87]); break;
                        case 89 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88]); break;
                        case 90 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89]); break;
                        case 91 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90]); break;
                        case 92 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91]); break;
                        case 93 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92]); break;
                        case 94 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93]); break;
                        case 95 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94]); break;
                        case 96 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95]); break;
                        case 97 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95],$resultarray[96]); break;
                        case 98 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95],$resultarray[96],$resultarray[97]); break;
                        case 99 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95],$resultarray[96],$resultarray[97],$resultarray[98]); break;
			}
			$Count=0;
			$ColumnCounter=0;
			while($stmt->fetch()) {
				for($i=0; $i<count($FieldNames); $i++)
				{
					$resultarray1[$Count][$FieldNames[$i]] = $resultarray[$i];
				}
				$Count++;
				
			  }
			  if($Count==0){
				  $resultarray1=null;
			  }
			  //var_export($result1);
			$stmt->close();
			return $resultarray1;
		}
		
		function fSelectRowCount($Query,$FieldNames,$sequence,$ParamArray)
		{
			
			$Count=0;
			
			$mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);
			$stmt = $mysqli->stmt_init();
			$stmt = $mysqli->prepare($Query);
			switch(count($ParamArray))
			{
			            case 0 : 
						break;
						case 1 : $stmt->bind_param($sequence, $ParamArray[0]);
						break;
						case 2 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1]);
						break;
						 case 3 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2]);
						 break;
						 case 4 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3]);
						 break;
						 case 5 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4]);
						 break;
			}
			
			//$stmt = $mysqli->bind_param($Query);
			$stmt->execute();
			$stmt->store_result();
		
			switch(count($FieldNames))
			{
			             case 0 : 
						break;
						case 1 : $stmt->bind_result($resultarray[0]); 
						break;
						case 2 : $stmt->bind_result($resultarray[0],$resultarray[1]); 
						break;
						 case 3 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2]); 
						 break;
						 case 4 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3]); 
						 break;
						 case 5 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4]); 
						 break;
						 case 6 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5]); 
						 break;
						 case 7 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6]); 
						 break;
						 case 8 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7]); 
						 break;
						 case 9 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8]); 
						 break;
						 case 10 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9]); 
						 break;
						 case 11 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10]); break;
                        case 12 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11]); break;
                        case 13 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12]); break;
                        case 14 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13]); break;
                        case 15 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14]); break;
                        case 16 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15]); break;
                        case 17 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16]); break;
                        case 18 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17]); break;
                        case 19 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18]); break;
                        case 20 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19]); break;
                        case 21 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20]); break;
                        case 22 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21]); break;
                        case 23 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22]); break;
                        case 24 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23]); break;
                        case 25 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24]); break;
                        case 26 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25]); break;
                        case 27 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26]); break;
                        case 28 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27]); break;
                        case 29 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28]); break;
                        case 30 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29]); break;
                        case 31 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30]); break;
                        case 32 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31]); break;
                        case 33 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32]); break;
                        case 34 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33]); break;
                        case 35 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34]); break;
                        case 36 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35]); break;
                        case 37 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36]); break;
                        case 38 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37]); break;
                        case 39 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38]); break;
                        case 40 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39]); break;
                        case 41 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40]); break;
                        case 42 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41]); break;
                        case 43 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42]); break;
                        case 44 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43]); break;
                        case 45 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44]); break;
                        case 46 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45]); break;
                        case 47 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46]); break;
                        case 48 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47]); break;
                        case 49 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48]); break;
                        case 50 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49]); break;
                        case 51 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50]); break;
                        case 52 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51]); break;
                        case 53 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52]); break;
                        case 54 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53]); break;
                        case 55 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54]); break;
                        case 56 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55]); break;
                        case 57 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56]); break;
                        case 58 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57]); break;
                        case 59 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58]); break;
                        case 60 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59]); break;
                        case 61 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60]); break;
                        case 62 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61]); break;
                        case 63 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62]); break;
                        case 64 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63]); break;
                        case 65 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64]); break;
                        case 66 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65]); break;
                        case 67 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66]); break;
                        case 68 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67]); break;
                        case 69 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68]); break;
                        case 70 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69]); break;
                        case 71 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70]); break;
                        case 72 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71]); break;
                        case 73 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72]); break;
                        case 74 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73]); break;
                        case 75 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74]); break;
                        case 76 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75]); break;
                        case 77 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76]); break;
                        case 78 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77]); break;
                        case 79 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78]); break;
                        case 80 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79]); break;
                        case 81 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80]); break;
                        case 82 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81]); break;
                        case 83 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82]); break;
                        case 84 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83]); break;
                        case 85 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84]); break;
                        case 86 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85]); break;
                        case 87 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86]); break;
                        case 88 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87]); break;
                        case 89 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88]); break;
                        case 90 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89]); break;
                        case 91 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90]); break;
                        case 92 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91]); break;
                        case 93 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92]); break;
                        case 94 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93]); break;
                        case 95 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94]); break;
                        case 96 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95]); break;
                        case 97 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95],$resultarray[96]); break;
                        case 98 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95],$resultarray[96],$resultarray[97]); break;
                        case 99 : $stmt->bind_result($resultarray[0],$resultarray[1],$resultarray[2],$resultarray[3],$resultarray[4],$resultarray[5],$resultarray[6],$resultarray[7],$resultarray[8],$resultarray[9],$resultarray[10],$resultarray[11],$resultarray[12],$resultarray[13],$resultarray[14],$resultarray[15],$resultarray[16],$resultarray[17],$resultarray[18],$resultarray[19],$resultarray[20],$resultarray[21],$resultarray[22],$resultarray[23],$resultarray[24],$resultarray[25],$resultarray[26],$resultarray[27],$resultarray[28],$resultarray[29],$resultarray[30],$resultarray[31],$resultarray[32],$resultarray[33],$resultarray[34],$resultarray[35],$resultarray[36],$resultarray[37],$resultarray[38],$resultarray[39],$resultarray[40],$resultarray[41],$resultarray[42],$resultarray[43],$resultarray[44],$resultarray[45],$resultarray[46],$resultarray[47],$resultarray[48],$resultarray[49],$resultarray[50],$resultarray[51],$resultarray[52],$resultarray[53],$resultarray[54],$resultarray[55],$resultarray[56],$resultarray[57],$resultarray[58],$resultarray[59],$resultarray[60],$resultarray[61],$resultarray[62],$resultarray[63],$resultarray[64],$resultarray[65],$resultarray[66],$resultarray[67],$resultarray[68],$resultarray[69],$resultarray[70],$resultarray[71],$resultarray[72],$resultarray[73],$resultarray[74],$resultarray[75],$resultarray[76],$resultarray[77],$resultarray[78],$resultarray[79],$resultarray[80],$resultarray[81],$resultarray[82],$resultarray[83],$resultarray[84],$resultarray[85],$resultarray[86],$resultarray[87],$resultarray[88],$resultarray[89],$resultarray[90],$resultarray[91],$resultarray[92],$resultarray[93],$resultarray[94],$resultarray[95],$resultarray[96],$resultarray[97],$resultarray[98]); break;
			}
			$Count=0;
			$ColumnCounter=0;
			while($stmt->fetch()) {
				$Count++;
				
			  }
			  //var_export($result1);
			$stmt->close();
			return $Count;
		}


        function fInsertNew($Query,$sequence,$ParamArray)
        {
    
            $mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);
    		$stmt = $mysqli->stmt_init();
        	$stmt = $mysqli->prepare($Query);
        	
        	switch(count($ParamArray))
			{
			            case 0 : 
						break;
						case 1 : $stmt->bind_param($sequence, $ParamArray[0]);
						break;
						case 2 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1]);
						break;
						 case 3 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2]);
						 break;
						 case 4 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3]);
						 break;
						 case 5 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4]);
						 break;
						 case 6 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5]);
						 break;
						 case 7 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6]);
						 break;
						 case 8 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7]);
						 break;
						 case 9 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8]);
						 break;
						 case 10 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9]);
						 break;
						 case 11 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10]);
						 break;
						 case 12 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11]);
						 break;
						 case 13 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12]);
						 break;
						 case 14 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13]);
						 break;
						 case 15 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14]);
						 break;
						 case 16 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15]);
						 break;
						 case 17 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16]);
						 break;
						 case 18 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17]);
						 break;
						 case 19 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18]);
						 break;
						 case 20 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19]);
						 break;
						 case 21 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20]);
						 break;
						 case 22 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21]);
						 break;
						 case 23 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22]);
						 break;
						 case 24 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23]);
						 break;
						 case 25 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24]);
						 break;
						 case 26 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25]);
						 break;
						 case 27 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26]);
						 break;
						 case 28 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27]);
						 break;
						 case 29 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28]);
						 break;
						 case 30 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29]);
						 break;
						 case 31 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13],$ParamArray[14],$ParamArray[15],$ParamArray[16],$ParamArray[17],$ParamArray[18],$ParamArray[19],$ParamArray[20],$ParamArray[21],$ParamArray[22],$ParamArray[23],$ParamArray[24],$ParamArray[25],$ParamArray[26],$ParamArray[27],$ParamArray[28],$ParamArray[29],$ParamArray[30]);
						 break;
			}
        	//$stmt->bind_param("sssss", $MobileNo, $EmailId, $ReferralCode,$CustomerCode,$OTP);
        	
        	$stmt->execute();
        	$stmt->close();
        	$CustomerId=$mysqli->insert_id;
        	return $CustomerId;
        }
        
        
        function fUpdateNew($Query,$sequence,$ParamArray)
        {
    
            $mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);
    		$stmt = $mysqli->stmt_init();
        	$stmt = $mysqli->prepare($Query);
        	
        	switch(count($ParamArray))
			{
			            case 0 : 
						break;
						case 1 : $stmt->bind_param($sequence, $ParamArray[0]);
						break;
						case 2 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1]);
						break;
						 case 3 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2]);
						 break;
						 case 4 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3]);
						 break;
						 case 5 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4]);
						 break;
						 case 6 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5]);
						 break;
						 case 7 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6]);
						 break;
						 case 8 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7]);
						 break;
						 case 9 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8]);
						 break;
						 case 10 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9]);
						 break;
						 case 11 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10]);
						 break;
						 case 12 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11]);
						 break;
						 case 13 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12]);
						 break;
						 case 14 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13]);
						 break;
			}
        	//$stmt->bind_param("sssss", $MobileNo, $EmailId, $ReferralCode,$CustomerCode,$OTP);
        	
        	$stmt->execute();
        	$stmt->close();
        	$CustomerId=$mysqli->insert_id;
        	return $CustomerId;
        }
        
        
        function fDeleteNew($Query,$sequence,$ParamArray)
        {
    
            $mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);
    		$stmt = $mysqli->stmt_init();
        	$stmt = $mysqli->prepare($Query);
        	
        	switch(count($ParamArray))
			{
			            case 0 : 
						break;
						case 1 : $stmt->bind_param($sequence, $ParamArray[0]);
						break;
						case 2 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1]);
						break;
						 case 3 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2]);
						 break;
						 case 4 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3]);
						 break;
						 case 5 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4]);
						 break;
						 case 6 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5]);
						 break;
						 case 7 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6]);
						 break;
						 case 8 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7]);
						 break;
						 case 9 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8]);
						 break;
						 case 10 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9]);
						 break;
						 case 11 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10]);
						 break;
						 case 12 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11]);
						 break;
						 case 13 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12]);
						 break;
						 case 14 : $stmt->bind_param($sequence, $ParamArray[0],$ParamArray[1],$ParamArray[2],$ParamArray[3],$ParamArray[4],$ParamArray[5],$ParamArray[6],$ParamArray[7],$ParamArray[8],$ParamArray[9],$ParamArray[10],$ParamArray[11],$ParamArray[12],$ParamArray[13]);
						 break;
			}
        	//$stmt->bind_param("sssss", $MobileNo, $EmailId, $ReferralCode,$CustomerCode,$OTP);
        	
        	$stmt->execute();
        	$stmt->close();
        	$CustomerId=$mysqli->insert_id;
        	return $CustomerId;
        }


		function check_user()
		{
			if(isset($this->data))
			{
				$array = json_decode($this->data, true);
				$query = "SELECT * FROM authenticate WHERE UserName='$array[user_name]' AND password='$array[password]'";
				$rows = mysql_query($query) or die(mysql_error());
				if(mysql_num_rows($rows) > 0)
				{
					$row = mysql_fetch_assoc($rows);
					$_SESSION['user_name'] = $row["user_name"];
					$_SESSION['login'] = $array["user_name"];
					global $siteURL;
					header('Location: '.$siteURL.'registration-form.php');
				}
			}
		}
		
		function fdelete($query)
		{
			if(mysql_query($query) )
			{
			$_SESSION['message']="Record Deleted successfully...";
			
			$_SESSION['success']='"success">Record Deleted succfully';
			
			}
			else
			{
			$_SESSION['message']="Record Not Deleted...";
			}
		}			
		function finsertfunction($query)
		{
			$myres=mysql_query($query);
			$_SESSION['message']=$query;
			if($myres)
				{
				$_SESSION['message']="Record Save successfully...";
				}
			else
				{
				$_SESSION['message']="Record Not Save ...".$query;
				}
				return $myres;

		}
		
		function multipleinsert($query)
		{
			
			if(mysql_query($query))
				{
				$_SESSION['message']="Record Save successfully...";
				}
			else
				{
				$_SESSION['message']="Record Not Save ...".$q;
				}
		}
		function finsert($q)
		{
			$query = "INSERT INTO ".$q;
			if(mysql_query($query))
				{
				$_SESSION['message']="Record Save successfully...";
				}
			else
				{
				$_SESSION['message']="Record Not Save ...".$query;
				}
		}
		function fupdate($query)
		{
			if(mysql_query($query) )
				{
				$_SESSION['message']="Record Update successfully...";
			
				}
			else
				{
				$_SESSION['message']="Record Not Updated ...";
				}
		}
		
		function select($query)
		{
			$sqldata = mysql_query($query) or die (mysql_error());
			$rows = array();
			while($r = mysql_fetch_assoc($sqldata))
			{
				$rows[] = $r;
			}
			$json=(json_encode($rows));
			 return ($json);
		}
		function fselectAllData($query)
		{
			$sqldata = mysql_query($query) or die (mysql_error());
			$rows = array();
			while($r = mysql_fetch_assoc($sqldata))
			{
				$rows[] = $r;
			}
			return ($rows);
			
		}
		function fSelectMax($query)
		{
			$max = mysql_query ( $query ) or die (mysql_error());
			$row = mysql_fetch_row($max);
    		$highest_id = $row[0];
			return ($highest_id);
		}
		
		function fSelectString($query)
		{
			$result=mysql_query($query) or die (mysql_error());
			$row=mysql_fetch_row($result);
			return ($row[0]);
		}
		function fSelectAll($query)
		{
			$result=mysql_query($query) or die (mysql_error());
			$row=mysql_fetch_row($result);
			return ($row);
		}
		function dateconversion($date)
		{
								list($date1,$time1)=explode(' ',$date);
								list($hour,$minute,$second)=explode(':',$time1);
									if($hour < 12)
										{
										$timeStatus='AM';
										$timeInAmPm = $hour.":".$minute;
										}
									else
										{
										$timeStatus='PM';
										$timeInAmPm = ($hour-12).":".$minute;
										}
									list($y,$m,$d)=explode('-',$date1);
									switch($m)
									{
									case 1:$month="January";break;
									case 2:$month="February";break;
									case 3:$month="March";break;
									case 2:$month="April";break;
									case 2:$month="May";break;
									case 2:$month="June";break;
									case 2:$month="July";break;
									case 2:$month="August";break;
									case 2:$month="September";break;
									case 2:$month="October";break;
									case 2:$month="November";break;
									case 2:$month="December";break;
									}
									return $month." ".$d." ".$y." ".$timeInAmPm."".$timeStatus;
		}
		function fselect($table)
		{
			$query = "SELECT * FROM ".$table;
			$rows = mysql_query($query) or die(mysql_error());
			$this->no_rows = 0;
			$arr = array();
			while($row = mysql_fetch_assoc($rows))
			{
				$arr[] = $row;
				$this->no_rows++;
			}
			$this->data = json_encode($arr);
		}
	}
	
	function MysqliSelectPriceFilter($min_price, $max_price)
		{
			// Initialize MySQLi object
			$mysqli = new mysqli(HOST, USER, PASSWORD, DATABASE);

			// Check connection
			if ($mysqli->connect_error) {
				throw new Exception("Database connection failed: " . $mysqli->connect_error);
			}

			// Create the query to fetch products within the specified price range
			$query = "
				SELECT p.ProductId, p.ProductName, p.PhotoPath
				FROM product_master p
				WHERE p.ProductId IN (
					SELECT pp.ProductId 
					FROM product_price pp 
					WHERE pp.OfferPrice BETWEEN ? AND ?
				)
			";

			// Prepare the query
			$stmt = $mysqli->prepare($query);
			if (!$stmt) {
				throw new Exception("Query preparation failed: " . $mysqli->error);
			}

			// Bind parameters dynamically (min_price and max_price as integers)
			$stmt->bind_param("ii", $min_price, $max_price);

			// Execute the statement
			if (!$stmt->execute()) {
				throw new Exception("Query execution failed: " . $stmt->error);
			}

			// Get the result set
			$result = $stmt->get_result();

			// Fetch all results into an associative array
			$resultArray = [];
			if ($result->num_rows > 0) {
				$resultArray = $result->fetch_all(MYSQLI_ASSOC);
			}

			// Close the statement and connection
			$stmt->close();
			$mysqli->close();

			return $resultArray;
		}
        
?>